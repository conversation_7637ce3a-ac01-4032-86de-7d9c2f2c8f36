{"name": "xcode build server", "version": "0.2", "bspVersion": "2.0", "languages": ["c", "cpp", "objective-c", "objective-cpp", "swift"], "argv": ["/opt/homebrew/bin/xcode-build-server"], "workspace": "/Users/<USER>/Desktop/13.4.0/ipaymentapp/ipaymentapp.xcworkspace", "build_root": "/Users/<USER>/Library/Developer/Xcode/DerivedData/ipaymentapp-blkrvsyglrczbpamlaobikhlciul", "kind": "xcode", "scheme": "ipaymentapp"}